.custom-material {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.material-property-group {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.material-property-group.column {
  flex-direction: column;
  align-items: flex-start;
}

.material-property-group.column .property-label {
  width: 100%;
  margin-bottom: 4px;
}

.property-label {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  width: 90px;
}

.color-picker-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

/* 自定义react-colorful组件的样式 */
.react-colorful {
  width: 100% !important;
  height: 146px !important;
  border-radius: var(--radius-base);
  overflow: visible; /* 允许控制杆完整显示 */
  outline: 1px var(--color-border) solid;
  outline-offset: -1px;
  margin-bottom: 8px;
}

/* 双色板样式 */
.react-colorful__saturation {
  border-radius: 0;
  border-bottom: none;
}

/* 色相条样式 */
.react-colorful__hue {
  height: 24px;
  border-radius: 0 0 var(--radius-base) var(--radius-base);
}

/* 调整选择器指示点样式 */
.custom-material .react-colorful__pointer {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: var(--shadow-base);
  border: 2px solid var(--color-content-invert);
}

.texture-upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 150px;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
}

.plus-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除宽高和背景，仅用于居中加号图标 */
}

.upload-text {
  font-size: var(--font-size-base);
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
  font-weight: 400;
}

.texture-preview-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.texture-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-texture-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 4px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.remove-texture-icon:hover {
  background: rgba(0, 0, 0, 0.8);
}