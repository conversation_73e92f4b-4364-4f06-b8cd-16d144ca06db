import React, { memo, useCallback, useRef, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialData } from '../../services/api';

interface MaterialThumbnailProps {
  material: MaterialData;
  active: boolean;
  onClick?: () => void;
  size?: 'default' | 'large';
  textureUrl?: string; // 添加 textureUrl 属性
}
import './material-thumbnail.css';



/**
 * 材质小缩略图（3D球体）
 * 为了性能，Canvas 使用 frameloop="demand"，首次渲染后停止自动渲染。
 */
const MaterialThumbnail: React.FC<MaterialThumbnailProps> = ({
  material,
  active,
  onClick,
  size = 'default',
  textureUrl,
}) => {
  const { color, metalness, roughness, glass } = material;
  const invalidateRef = useRef<(() => void) | null>(null);

  const handleCreated = useCallback(({ invalidate }: { invalidate: () => void }) => {
    // 保存 invalidate，在 Sphere 材质准备好后调用一次使其渲染首帧
    invalidateRef.current = invalidate;
  }, []);

  // 当材质属性变化时，触发重新渲染
  useEffect(() => {
    if (invalidateRef.current) {
      invalidateRef.current();
    }
  }, [color, metalness, roughness, glass, textureUrl]);

  return (
    <div
      className={`material-item${active ? ' active' : ''} ${size === 'large' ? 'material-item-large' : ''}`}
      onClick={onClick}
    >
      <div className="thumbnail-canvas">
      <Canvas
        frameloop="demand"
        camera={{ position: [0, 0, 1.5], fov: 45 }}
        onCreated={handleCreated}
        gl={{ antialias: true, alpha: true }}
      >
        {/* 环境光/主光源 */}
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={1} />

        {/* 材质球 */}
        <mesh
          onUpdate={() => {
            // 渲染一次首帧
            invalidateRef.current?.();
          }}
        >
          <sphereGeometry args={[1, 64, 64]} />
          <meshStandardMaterial
            color={new THREE.Color(color)}
            metalness={metalness / 100}
            roughness={roughness / 100}
            transparent={glass > 0}
            opacity={glass > 0 ? (100 - glass) / 100 : 1}
            map={textureUrl ? new THREE.TextureLoader().load(textureUrl) : null}
          />
        </mesh>

        {/* 使用城市 HDR 环境 */}
        <Environment preset="city" />
      </Canvas>
      </div>
    </div>
  );
};

export default memo(MaterialThumbnail, (prevProps, nextProps) => {
  // 只有当material的关键属性、active状态或size都相同时才跳过重新渲染
  return (
    prevProps.material.color === nextProps.material.color &&
    prevProps.material.metalness === nextProps.material.metalness &&
    prevProps.material.roughness === nextProps.material.roughness &&
    prevProps.material.glass === nextProps.material.glass &&
    prevProps.textureUrl === nextProps.textureUrl &&
    prevProps.active === nextProps.active &&
    prevProps.size === nextProps.size
  );
});
