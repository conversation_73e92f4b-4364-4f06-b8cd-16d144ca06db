.tooltipContainer {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  animation: fadeIn 0.2s ease-in-out;
}

.tooltip::before {
  content: '';
  position: absolute;
  border: 6px solid transparent;
}

/* 顶部提示 */
.top::before {
  border-top-color: rgba(0, 0, 0, 0.8);
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
}

/* 底部提示 */
.bottom::before {
  border-bottom-color: rgba(0, 0, 0, 0.8);
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

/* 左侧提示 */
.left::before {
  border-left-color: rgba(0, 0, 0, 0.8);
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
}

/* 右侧提示 */
.right::before {
  border-right-color: rgba(0, 0, 0, 0.8);
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}