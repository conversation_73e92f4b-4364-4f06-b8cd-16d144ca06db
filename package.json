{"name": "huitong-material", "private": true, "version": "0.0.0", "type": "module", "workspaces": ["backend"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:unused": "ts-prune", "prepare": "husky install", "preview": "vite preview", "start": "concurrently \"npm run dev\" \"cd backend && npm start\""}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@types/three": "^0.177.0", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "ts-prune": "^0.10.3", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.0.10", "lint-staged": "^15.2.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix"]}}